"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Bot, Brain, CheckCircle, Clock, AlertCircle, TrendingUp, FileText, Zap, Target, BookOpen, Settings, Eye, Sparkles, BarChart3, RefreshCw, Download, Filter } from "lucide-react";

// TODO: Replace with real AI implementation - currently dummy data
const mockPendingBookings = [
  {
    id: 1,
    date: "2024-01-15",
    description: "Amazon Business Rechnung",
    amount: 89.99,
    suggestedAccount: "6300 - Büromaterial",
    confidence: 98,
    category: "Betriebsausgaben",
    status: "pending",
    documentType: "Rechnung",
    supplier: "Amazon Business"
  },
  {
    id: 2,
    date: "2024-01-14",
    description: "Shell Tankstelle",
    amount: 65.50,
    suggestedAccount: "6540 - Fahrtkosten",
    confidence: 95,
    category: "Fahrzeuge",
    status: "pending",
    documentType: "Beleg",
    supplier: "Shell"
  },
  {
    id: 3,
    date: "2024-01-13",
    description: "Telekom Rechnung",
    amount: 89.90,
    suggestedAccount: "6200 - Telefon/Internet",
    confidence: 99,
    category: "Kommunikation",
    status: "pending",
    documentType: "Rechnung",
    supplier: "Telekom"
  },
  {
    id: 4,
    date: "2024-01-12",
    description: "Office Depot Büromaterial",
    amount: 156.78,
    suggestedAccount: "6300 - Büromaterial",
    confidence: 92,
    category: "Betriebsausgaben",
    status: "pending",
    documentType: "Rechnung",
    supplier: "Office Depot"
  }
];

const mockProcessedBookings = [
  {
    id: 5,
    date: "2024-01-12",
    description: "Miete Büroräume",
    amount: 1200.00,
    account: "6200 - Mieten",
    confidence: 100,
    processedAt: "2024-01-12 14:30",
    status: "processed",
    documentType: "Rechnung",
    supplier: "Immobilien GmbH"
  },
  {
    id: 6,
    date: "2024-01-11",
    description: "Gehalt Mitarbeiter",
    amount: 3500.00,
    account: "6000 - Löhne und Gehälter",
    confidence: 100,
    processedAt: "2024-01-11 09:15",
    status: "processed",
    documentType: "Lohnabrechnung",
    supplier: "Intern"
  },
  {
    id: 7,
    date: "2024-01-10",
    description: "Microsoft Office 365",
    amount: 89.90,
    account: "6300 - Software",
    confidence: 98,
    processedAt: "2024-01-10 16:45",
    status: "processed",
    documentType: "Rechnung",
    supplier: "Microsoft"
  }
];

const mockLearningStats = {
  totalBookings: 1247,
  automaticRate: 87,
  accuracyRate: 96,
  timeSaved: 42.5,
  corrections: 23,
  weeklyGrowth: 12,
  monthlyProcessed: 156,
  errorRate: 4,
  avgProcessingTime: 2.3,
  costSavings: 2125
};

const mockAccountSuggestions = [
  { account: "6300 - Büromaterial", usage: 45, accuracy: 98, trend: "up" },
  { account: "6540 - Fahrtkosten", usage: 38, accuracy: 95, trend: "stable" },
  { account: "6200 - Telefon/Internet", usage: 32, accuracy: 99, trend: "up" },
  { account: "6000 - Löhne und Gehälter", usage: 28, accuracy: 100, trend: "stable" },
  { account: "6400 - Werbekosten", usage: 22, accuracy: 92, trend: "down" }
];

const mockAIInsights = [
  {
    type: "pattern",
    title: "Neue Ausgabenkategorie erkannt",
    description: "Häufige Ausgaben für 'Home Office Equipment' identifiziert",
    action: "Neue Kategorie vorschlagen",
    priority: "medium"
  },
  {
    type: "optimization",
    title: "Kosteneinsparung möglich",
    description: "Doppelte Abonnements bei Software-Lizenzen entdeckt",
    action: "Überprüfung empfohlen",
    priority: "high"
  },
  {
    type: "compliance",
    title: "Steuerliche Optimierung",
    description: "Mögliche Abschreibungen für IT-Equipment nicht genutzt",
    action: "Steuerberater kontaktieren",
    priority: "medium"
  }
];

export default function KIBuchhalterPage() {
  const [processingAll, setProcessingAll] = useState(false);
  const [pendingBookings, setPendingBookings] = useState(mockPendingBookings);
  const [filterCategory, setFilterCategory] = useState("all");
  const [autoProcessing, setAutoProcessing] = useState(true);
  const [confidenceThreshold, setConfidenceThreshold] = useState(95);
  const [showInsights, setShowInsights] = useState(true);

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      if (Math.random() > 0.95) {
        // Simulate new booking
        const newBooking = {
          id: Date.now(),
          date: new Date().toISOString().split('T')[0],
          description: "Neue automatisch erkannte Buchung",
          amount: Math.round(Math.random() * 500 + 50),
          suggestedAccount: "6300 - Büromaterial",
          confidence: Math.round(Math.random() * 20 + 80),
          category: "Betriebsausgaben",
          status: "pending",
          documentType: "Rechnung",
          supplier: "Automatisch erkannt"
        };
        setPendingBookings(prev => [newBooking, ...prev]);
      }
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  const handleProcessAll = () => {
    setProcessingAll(true);
    // TODO: Implement real AI processing
    setTimeout(() => {
      setProcessingAll(false);
      setPendingBookings([]);
    }, 3000);
  };

  const handleApproveBooking = (id: number) => {
    setPendingBookings(prev => prev.filter(booking => booking.id !== id));
  };

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 95) return <Badge className="bg-green-500/20 text-green-400 border-green-500/30">Sehr sicher ({confidence}%)</Badge>;
    if (confidence >= 85) return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">Sicher ({confidence}%)</Badge>;
    return <Badge className="bg-red-500/20 text-red-400 border-red-500/30">Unsicher ({confidence}%)</Badge>;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(amount);
  };

  const filteredBookings = filterCategory === "all"
    ? pendingBookings
    : pendingBookings.filter(booking => booking.category === filterCategory);

  return (
    <div className="space-y-6">
      {/* Header with improved design */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
              <Bot className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-3xl font-bold tracking-tight">KI-Buchhalter</h1>
          </div>
          <p className="text-muted-foreground text-lg">
            Automatische Buchhaltung mit künstlicher Intelligenz
          </p>
          <div className="mt-3 flex flex-wrap gap-2">
            <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
              <Sparkles className="h-3 w-3 mr-1" />
              Beta Version
            </Badge>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <CheckCircle className="h-3 w-3 mr-1" />
              {mockLearningStats.accuracyRate}% Genauigkeit
            </Badge>
          </div>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Aktualisieren
          </Button>
          <Button
            onClick={handleProcessAll}
            disabled={processingAll || pendingBookings.length === 0}
            className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
          >
            <Bot className="h-4 w-4 mr-2" />
            {processingAll ? "Verarbeite..." : "Alle automatisch buchen"}
          </Button>
        </div>
      </div>

      {/* AI Insights Banner */}
      {showInsights && mockAIInsights.length > 0 && (
        <Card className="border-blue-500/20 bg-gradient-to-r from-blue-950/30 to-purple-950/30">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-blue-400" />
                <CardTitle className="text-lg text-foreground">KI-Erkenntnisse</CardTitle>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowInsights(false)}
                className="text-muted-foreground hover:text-foreground"
              >
                ×
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-3">
              {mockAIInsights.map((insight, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-card/50 rounded-lg border border-border/50">
                  <div className={`p-1 rounded-full ${
                    insight.priority === 'high' ? 'bg-red-500/20 text-red-400' :
                    insight.priority === 'medium' ? 'bg-yellow-500/20 text-yellow-400' : 'bg-green-500/20 text-green-400'
                  }`}>
                    {insight.type === 'pattern' && <BarChart3 className="h-4 w-4" />}
                    {insight.type === 'optimization' && <Zap className="h-4 w-4" />}
                    {insight.type === 'compliance' && <Eye className="h-4 w-4" />}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-sm text-foreground">{insight.title}</p>
                    <p className="text-xs text-muted-foreground mt-1">{insight.description}</p>
                    <Button variant="link" size="sm" className="h-auto p-0 text-xs mt-1 text-blue-400 hover:text-blue-300">
                      {insight.action}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced KI Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Automatisierungsgrad</CardTitle>
            <Bot className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockLearningStats.automaticRate}%</div>
            <div className="flex items-center text-xs text-muted-foreground mt-1">
              <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              +{mockLearningStats.weeklyGrowth}% diese Woche
            </div>
            <Progress value={mockLearningStats.automaticRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Genauigkeit</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockLearningStats.accuracyRate}%</div>
            <div className="flex items-center text-xs text-muted-foreground mt-1">
              <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
              Fehlerrate: {mockLearningStats.errorRate}%
            </div>
            <Progress value={mockLearningStats.accuracyRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Zeitersparnis</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockLearningStats.timeSaved}h</div>
            <div className="flex items-center text-xs text-muted-foreground mt-1">
              <Clock className="h-3 w-3 mr-1 text-blue-500" />
              Ø {mockLearningStats.avgProcessingTime}s pro Beleg
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Kosteneinsparung</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(mockLearningStats.costSavings)}</div>
            <div className="flex items-center text-xs text-muted-foreground mt-1">
              <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              {mockLearningStats.monthlyProcessed} Belege diesen Monat
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="pending" className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <TabsList className="grid w-full grid-cols-4 lg:w-auto">
            <TabsTrigger value="pending" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Warteschlange ({filteredBookings.length})
            </TabsTrigger>
            <TabsTrigger value="processed" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Verarbeitet
            </TabsTrigger>
            <TabsTrigger value="learning" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              KI-Lernen
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Einstellungen
            </TabsTrigger>
          </TabsList>

          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-3 py-1 border border-border rounded-md text-sm bg-card text-foreground"
            >
              <option value="all">Alle Kategorien</option>
              <option value="Betriebsausgaben">Betriebsausgaben</option>
              <option value="Fahrzeuge">Fahrzeuge</option>
              <option value="Kommunikation">Kommunikation</option>
            </select>
          </div>
        </div>

        <TabsContent value="pending" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Buchungsvorschläge der KI</CardTitle>
                  <CardDescription>
                    Automatisch erkannte Buchungen zur Freigabe
                  </CardDescription>
                </div>
                <Badge variant="outline" className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                  {filteredBookings.length} ausstehend
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              {filteredBookings.length === 0 ? (
                <div className="text-center py-12">
                  <div className="mx-auto w-24 h-24 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
                    <CheckCircle className="h-12 w-12 text-green-400" />
                  </div>
                  <h3 className="text-lg font-medium mb-2 text-foreground">Alle Buchungen verarbeitet!</h3>
                  <p className="text-muted-foreground mb-4">Die KI hat alle ausstehenden Belege automatisch gebucht.</p>
                  <Button variant="outline" className="border-border hover:bg-accent">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Nach neuen Belegen suchen
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredBookings.map((booking) => (
                    <div key={booking.id} className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-accent/50 transition-colors">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-blue-500/20 rounded-lg">
                          <Brain className="h-6 w-6 text-blue-400" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="font-medium text-foreground">{booking.description}</p>
                            <Badge variant="outline" className="text-xs border-border">
                              {booking.documentType}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>{booking.date}</span>
                            <span>•</span>
                            <span>{booking.supplier}</span>
                            <span>•</span>
                            <span>{booking.category}</span>
                          </div>
                          <p className="text-sm text-blue-400 mt-1">
                            Vorschlag: {booking.suggestedAccount}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="font-medium text-lg text-foreground">{formatCurrency(booking.amount)}</p>
                          {getConfidenceBadge(booking.confidence)}
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            onClick={() => handleApproveBooking(booking.id)}
                            className="bg-green-600 hover:bg-green-700 text-white"
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Buchen
                          </Button>
                          <Button size="sm" variant="outline" className="border-border hover:bg-accent">
                            <Settings className="h-4 w-4 mr-1" />
                            Korrigieren
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="processed" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Automatisch verarbeitete Buchungen</CardTitle>
                  <CardDescription>
                    Von der KI erfolgreich gebuchte Belege
                  </CardDescription>
                </div>
                <Badge variant="outline" className="bg-green-500/20 text-green-400 border-green-500/30">
                  {mockProcessedBookings.length} verarbeitet
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockProcessedBookings.map((booking) => (
                  <div key={booking.id} className="flex items-center justify-between p-4 border border-border rounded-lg bg-green-500/10">
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-green-500/20 rounded-lg">
                        <CheckCircle className="h-6 w-6 text-green-400" />
                      </div>
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-medium text-foreground">{booking.description}</p>
                          <Badge variant="outline" className="text-xs border-border">
                            {booking.documentType}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>{booking.date}</span>
                          <span>•</span>
                          <span>{booking.supplier}</span>
                        </div>
                        <p className="text-sm text-green-400 mt-1">Gebucht auf: {booking.account}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-lg text-foreground">{formatCurrency(booking.amount)}</p>
                      <p className="text-xs text-muted-foreground">Verarbeitet: {booking.processedAt}</p>
                      <Badge className="bg-green-500/20 text-green-400 border-green-500/30 mt-1">
                        Automatisch ({booking.confidence}%)
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="learning" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>KI-Lernfortschritt</CardTitle>
                <CardDescription>
                  Wie die KI aus Ihren Korrekturen lernt
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Lernrate</span>
                    <span className="text-sm text-green-600 flex items-center">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      +{mockLearningStats.weeklyGrowth}% diese Woche
                    </span>
                  </div>
                  <Progress value={87} className="h-2" />

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="text-center p-3 bg-accent/50 rounded-lg border border-border/50">
                      <div className="font-medium text-lg text-foreground">{mockLearningStats.corrections}</div>
                      <div className="text-muted-foreground">Korrekturen diese Woche</div>
                    </div>
                    <div className="text-center p-3 bg-accent/50 rounded-lg border border-border/50">
                      <div className="font-medium text-lg text-green-400">+3%</div>
                      <div className="text-muted-foreground">Genauigkeitsverbesserung</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">Letzte Verbesserungen</h4>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-sm">
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        <span>Neue Lieferanten-Erkennung implementiert</span>
                        <Badge variant="outline" className="text-xs border-border">+5% Genauigkeit</Badge>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-foreground">
                        <Brain className="h-4 w-4 text-blue-400" />
                        <span>Kostenstellen-Zuordnung verbessert</span>
                        <Badge variant="outline" className="text-xs border-border">+8% Automatisierung</Badge>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-foreground">
                        <Zap className="h-4 w-4 text-purple-400" />
                        <span>Duplikatserkennung optimiert</span>
                        <Badge variant="outline" className="text-xs border-border">100% Trefferquote</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Häufigste Konten</CardTitle>
                <CardDescription>
                  Von der KI am meisten verwendete Buchungskonten
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockAccountSuggestions.map((account, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <div className="text-sm font-medium text-foreground">{account.account}</div>
                          {account.trend === 'up' && <TrendingUp className="h-3 w-3 text-green-400" />}
                          {account.trend === 'down' && <TrendingUp className="h-3 w-3 text-red-400 rotate-180" />}
                        </div>
                        <Progress value={(account.usage / 50) * 100} className="h-1" />
                      </div>
                      <div className="ml-4 text-right">
                        <div className="text-sm font-medium text-foreground">{account.usage}x</div>
                        <div className="text-xs text-green-400">{account.accuracy}%</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Automatisierungs-Einstellungen</CardTitle>
                <CardDescription>
                  Konfiguration der KI-Buchungsregeln
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="auto-processing">Automatische Verarbeitung</Label>
                    <p className="text-sm text-muted-foreground">Buchungen automatisch verarbeiten</p>
                  </div>
                  <Switch
                    id="auto-processing"
                    checked={autoProcessing}
                    onCheckedChange={setAutoProcessing}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confidence-threshold">
                    Konfidenz-Schwellenwert: {confidenceThreshold}%
                  </Label>
                  <Input
                    id="confidence-threshold"
                    type="range"
                    min="80"
                    max="99"
                    value={confidenceThreshold}
                    onChange={(e) => setConfidenceThreshold(Number(e.target.value))}
                    className="w-full"
                  />
                  <p className="text-sm text-muted-foreground">
                    Buchungen ab dieser Konfidenz werden automatisch verarbeitet
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-foreground">Maximaler Betrag</span>
                    <span className="text-sm text-foreground">500€</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-foreground">Lernmodus aktiv</span>
                    <Badge className="bg-green-500/20 text-green-400 border-green-500/30">Ein</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Benachrichtigungen</CardTitle>
                <CardDescription>
                  Wann soll die KI Sie informieren?
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-foreground">Bei unsicheren Buchungen</p>
                      <p className="text-xs text-muted-foreground">Konfidenz unter 85%</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-foreground">Bei neuen Lieferanten</p>
                      <p className="text-xs text-muted-foreground">Unbekannte Rechnungssteller</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-foreground">Tägliche Zusammenfassung</p>
                      <p className="text-xs text-muted-foreground">E-Mail um 18:00 Uhr</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center space-x-3">
                    <AlertCircle className="h-4 w-4 text-yellow-400" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-foreground">KI-Erkenntnisse</p>
                      <p className="text-xs text-muted-foreground">Optimierungsvorschläge</p>
                    </div>
                    <Switch checked={showInsights} onCheckedChange={setShowInsights} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
